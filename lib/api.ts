import ky from "ky";
import { showErrorToast } from "~/components/toast";

import { BASE_API_URL_CLIENT } from "~/constants/base-url";
import { setStorageItemAsync } from "~/modules/hooks/useStorageState";
import {
  getSession,
  signOut,
  UPACE_TOKEN,
} from "~/modules/login/auth-provider";
import { trackApiError } from "./error-tracking";
import { router } from "expo-router";

export const api = ky.create({
  prefixUrl: BASE_API_URL_CLIENT,
  headers: {
    Accept: "application/json",
    "Content-Type": "application/json",
  },
  hooks: {
    beforeRequest: [
      async (request, _options) => {
        const session = await getSession();
        if (!session?.token) {
          // If there's no token, we're probably logged out.
          // We'll cancel the request to avoid unnecessary network calls.
          throw ky.stop; // Stop the request from proceeding
        }
        request.headers.set("Authorization", `Bearer ${session.token}`);
      },
    ],
    afterResponse: [
      async (request, _options, response) => {
        const resp = (await response.json()) as {
          code: number;
          reason: string;
        };

        if (resp?.code === 401 && resp?.reason === "Invalid Token") {
          const res = await refreshToken();
          request.headers.set("Authorization", `Bearer ${res?.token}`);
          return ky(request);
        }
      },
    ],
    beforeError: [
      async (error) => {
        const errorResponse = (await error.response.json()) as {
          message: string;
        };

        // Track API error
        trackApiError(
          error,
          error.request.url.replace(BASE_API_URL_CLIENT, ""),
          error.request.method,
          { message: errorResponse?.message }
        );

        if (error.request.url.includes("auth/token/refresh")) {
          signOut();
          router.replace("/(auth)/sign-in");
          return errorResponse?.message as never;
        }

        showErrorToast(errorResponse?.message);
        return errorResponse?.message as never;
      },
    ],
  },
});

const refreshToken = async () => {
  try {
    const session = await getSession();

    const resp = await api
      .post<{ message: string; success: boolean; token: string }>(
        "auth/token/refresh",
        {
          json: {
            refresh_token: session?.refresh_token,
          },
        }
      )
      .json();

    setStorageItemAsync(UPACE_TOKEN, {
      ...session,
      token: resp?.token,
    });
    return resp;
  } catch (error: unknown) {
    throw error;
    // showErrorToast("Session expired, you have been logged out.");
  }
};
