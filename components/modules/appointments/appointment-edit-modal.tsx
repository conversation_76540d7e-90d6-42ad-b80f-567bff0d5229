import React, { useMemo, useState } from "react";
import { View, ScrollView } from "react-native";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { DatePicker } from "~/components/modules/common/date-picker";
import { formatClassDate } from "~/modules/classes/utils";
import { useUpdateReservation } from "~/modules/classes/mutations/useUpdateReservation";
import { parseISO } from "date-fns";
import { useTrainerData } from "~/modules/appointments/queries/useTrainerData";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form, FormField, FormMessage } from "~/components/ui/form";
import { AutocompleteDropdownContextProvider } from "react-native-autocomplete-dropdown";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import { getCalendarEventId } from "~/modules/calendar/calendar-storage";
import { updateCalendarEvent } from "~/modules/calendar/calendar-utils";
import { showSuccessToast, showErrorToast } from "~/components/toast";
import { ModalBottomSheet } from "~/components/modules/common/modal-bottom-sheet";
import { BaseSelect, Select } from "~/components/ui/select";

// Define the form schema
const EditReservationSchema = z.object({
  date: z.date({
    message: "Date is required",
  }),
  time: z.object(
    {
      value: z.string(),
      label: z.string(),
    },
    { message: "Time is required" }
  ),
});

type EditReservationFormValues = z.infer<typeof EditReservationSchema>;

interface SimpleEditModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  reservationId: number;
  currentDate: string;
  onSuccess?: () => void;
  equipment_id?: number;
  userId?: number;
}

export function AppointmentEditModal({
  isOpen,
  setIsOpen,
  reservationId,
  currentDate,
  onSuccess,
  equipment_id,
  userId,
}: SimpleEditModalProps) {
  const { trackEvent, EVENTS } = useAnalytics();

  const parsedDate = parseISO(currentDate);

  const form = useForm<EditReservationFormValues>({
    resolver: zodResolver(EditReservationSchema),
    defaultValues: {
      date: parsedDate,
    },
  });

  const selectedDate = form.watch("date") || parsedDate;
  const [selectedTime, setSelectedTime] = useState<
    | {
        value: string;
        label: string;
      }
    | undefined
  >();

  const { data } = useTrainerData({
    date: formatClassDate(selectedDate),
    equipment_id: String(equipment_id),
  });

  const options = useMemo(
    () =>
      data?.availableTimeBlocks?.map((item) => ({
        value: item.id,
        label: item.title,
      })) || [],
    [data?.availableTimeBlocks]
  );

  const { mutate: updateReservation, isPending: isUpdating } =
    useUpdateReservation(async () => {
      try {
        const calendarEventId = await getCalendarEventId(reservationId);

        if (calendarEventId) {
          const formValues = form.getValues();

          const [startHour, startMinute] = selectedTime!.value
            .split(":")
            .map(Number);

          const startDate = new Date(formValues.date);
          startDate.setHours(startHour, startMinute, 0, 0);

          const endDate = new Date(startDate);
          endDate.setHours(startDate.getHours() + 1);

          const updated = await updateCalendarEvent(
            calendarEventId,
            `Appointment with Member`,
            startDate.toISOString(),
            endDate.toISOString(),
            "",
            "Personal training appointment"
          );

          if (updated) {
            showSuccessToast("Appointment and calendar event updated");
          } else {
            showErrorToast(
              "Appointment updated, but calendar event could not be updated"
            );
          }
        }
      } catch (error) {
        console.error("Error updating calendar event:", error);
        showErrorToast(
          "Appointment updated, but calendar event could not be updated"
        );
      }

      setIsOpen(false);
      if (onSuccess) onSuccess();
    });

  const handleUpdate = () => {
    if (!selectedTime) return;

    form.setValue("time", selectedTime);
    const formValues = form.getValues();

    const newStartTime = `${formatClassDate(formValues.date)} ${
      formValues.time.value
    }`;

    updateReservation({
      user_id: userId as number,
      reservation_id: reservationId,
      equipment_id: String(equipment_id),
      type: "pt",
      start_time: newStartTime,
    });

    // Track appointment edit event
    trackEvent(EVENTS.EDIT_APPOINTMENT, {
      reservation_id: reservationId,
      user_id: userId,
      equipment_id: equipment_id,
      new_date: formatClassDate(formValues.date),
      new_time: formValues.time.label,
      old_date: currentDate,
    });
  };

  return (
    <ModalBottomSheet
      isVisible={isOpen}
      onClose={() => setIsOpen(false)}
      title="Edit Reservation Date and Time"
      height={550}
    >
      <>
        <AutocompleteDropdownContextProvider>
          <Form {...form}>
            <ScrollView
              className="flex-1"
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
            >
              <View className="space-y-6">
                <FormField
                  control={form.control}
                  name="date"
                  render={({ field }) => (
                    <View>
                      <DatePicker
                        label="Date"
                        value={field.value}
                        onChange={(date) => {
                          field.onChange(date);
                          setSelectedTime(undefined);
                        }}
                      />
                      <FormMessage />
                    </View>
                  )}
                />

                <View>
                  <Text
                    className="text-sm font-medium mb-2"
                    accessibilityLabel="Time selection"
                    accessibilityRole="text"
                  >
                    Time
                  </Text>
                  <View
                    style={{
                      borderRadius: 5,

                      marginBottom: 10,
                      zIndex: 9999,
                      elevation: 1000,
                      flex: 1,
                    }}
                  >
                    <BaseSelect
                      options={options}
                      label="Select time"
                      onSelect={(item) => {
                        if (item) {
                          setSelectedTime(item);
                        }
                      }}
                      value={selectedTime ?? undefined}
                    />
                  </View>
                </View>
              </View>
            </ScrollView>

            <View className="flex flex-row mt-4 gap-3 pt-4 border-t border-gray-200">
              <Button
                onPress={() => setIsOpen(false)}
                variant="secondary"
                className="flex-1"
                label="Cancel"
                accessibilityLabel="Cancel editing"
                accessibilityRole="button"
                accessibilityHint="Cancels editing and closes this dialog"
              />
              <Button
                onPress={handleUpdate}
                disabled={isUpdating || !selectedTime}
                isLoading={isUpdating}
                className="flex-1 bg-[#002966]"
                label="Update"
                accessibilityLabel="Update appointment"
                accessibilityRole="button"
                accessibilityHint="Saves the changes to this appointment"
              />
            </View>
          </Form>
        </AutocompleteDropdownContextProvider>
      </>
    </ModalBottomSheet>
  );
}
