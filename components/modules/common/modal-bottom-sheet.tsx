import { BlurView } from "expo-blur";

import {
  DimensionValue,
  Pressable,
  SafeAreaView,
  View,
  Modal,
} from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";

import AntDesign from "@expo/vector-icons/AntDesign";
import { Text } from "~/components/ui/text";
import { useColorScheme } from "~/lib/useColorScheme";

interface ModalBottomSheetProps {
  children: React.ReactNode;
  title?: string;
  height?: DimensionValue;
  isVisible: boolean;
  onClose: () => void;
}

export const ModalBottomSheet = ({
  children,
  title,
  height,
  isVisible,
  onClose,
}: ModalBottomSheetProps) => {
  const { isDarkColorScheme } = useColorScheme();

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
      accessibilityViewIsModal={true}
    >
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaView className="flex-1">
          <Pressable
            className="flex-1"
            onPress={onClose}
            accessibilityLabel="Close modal"
            accessibilityRole="button"
            accessibilityHint="Tap to close this modal"
          >
            <View />
          </Pressable>
          <BlurView
            experimentalBlurMethod="dimezisBlurView"
            intensity={isDarkColorScheme ? 60 : 95}
            tint="light"
            style={{
              height: height ?? "auto",
              width: "100%",
              position: "relative",
              bottom: 0,
              elevation: 8,
              shadowColor: "#000",
              shadowRadius: 8,
              shadowOpacity: 0.15,
              paddingTop: 16,
              paddingHorizontal: 16,
              paddingBottom: 16,
            }}
            accessibilityLabel={title ? `${title} dialog` : "Dialog"}
          >
            <View className="flex-row justify-between items-center mb-4">
              <View className="flex-1">
                {title && (
                  <Text
                    className="text-lg font-bold"
                    accessibilityRole="header"
                    accessibilityLabel={title}
                  >
                    {title}
                  </Text>
                )}
              </View>
              <Pressable
                className="p-2"
                onPress={onClose}
                accessibilityLabel="Close dialog"
                accessibilityRole="button"
                accessibilityHint="Closes this dialog"
              >
                <AntDesign
                  name="close"
                  size={20}
                  color={isDarkColorScheme ? "white" : "black"}
                />
              </Pressable>
            </View>
            <View className="flex-1 ">{children}</View>
          </BlurView>
        </SafeAreaView>
      </GestureHandlerRootView>
    </Modal>
  );
};
